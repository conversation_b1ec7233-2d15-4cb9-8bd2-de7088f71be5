package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="DebugTemplatedQueryResultResponse")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class DebugTemplatedQueryResultResponse extends java.lang.Object  {

  /**
   * Statically creates a builder instance for DebugTemplatedQueryResultResponse.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of DebugTemplatedQueryResultResponse.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String result;
    /**
     * Sets the value of the field "result" to be used for the constructed object.
     * @param result
     *   The value of the "result" field.
     * @return
     *   This builder.
     */
    public Builder withResult(String result) {
      this.result = result;
      return this;
    }

    protected String continuationToken;
    /**
     * Sets the value of the field "continuationToken" to be used for the constructed object.
     * @param continuationToken
     *   The value of the "continuationToken" field.
     * @return
     *   This builder.
     */
    public Builder withContinuationToken(String continuationToken) {
      this.continuationToken = continuationToken;
      return this;
    }

    protected String completionDateTime;
    /**
     * Sets the value of the field "completionDateTime" to be used for the constructed object.
     * @param completionDateTime
     *   The value of the "completionDateTime" field.
     * @return
     *   This builder.
     */
    public Builder withCompletionDateTime(String completionDateTime) {
      this.completionDateTime = completionDateTime;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(DebugTemplatedQueryResultResponse instance) {
      instance.setResult(this.result);
      instance.setContinuationToken(this.continuationToken);
      instance.setCompletionDateTime(this.completionDateTime);
    }

    /**
     * Builds an instance of DebugTemplatedQueryResultResponse.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public DebugTemplatedQueryResultResponse build() {
      DebugTemplatedQueryResultResponse instance = new DebugTemplatedQueryResultResponse();

      populate(instance);

      return instance;
    }
  };

  private String result;
  private String continuationToken;
  private String completionDateTime;

  @Required()
  public String getResult() {
    return this.result;
  }

  public void setResult(String result) {
    this.result = result;
  }

  public String getContinuationToken() {
    return this.continuationToken;
  }

  public void setContinuationToken(String continuationToken) {
    this.continuationToken = continuationToken;
  }

  @Required()
  public String getCompletionDateTime() {
    return this.completionDateTime;
  }

  public void setCompletionDateTime(String completionDateTime) {
    this.completionDateTime = completionDateTime;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.DebugTemplatedQueryResultResponse");

  /**
   * HashCode implementation for DebugTemplatedQueryResultResponse
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getResult(),
        getContinuationToken(),
        getCompletionDateTime());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for DebugTemplatedQueryResultResponse
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof DebugTemplatedQueryResultResponse)) {
      return false;
    }

    DebugTemplatedQueryResultResponse that = (DebugTemplatedQueryResultResponse) other;

    return
        Objects.equals(getResult(), that.getResult())
        && Objects.equals(getContinuationToken(), that.getContinuationToken())
        && Objects.equals(getCompletionDateTime(), that.getCompletionDateTime());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("DebugTemplatedQueryResultResponse(");

    ret.append("result=");
    ret.append(String.valueOf(result));
    ret.append(", ");

    ret.append("continuationToken=");
    ret.append(String.valueOf(continuationToken));
    ret.append(", ");

    ret.append("completionDateTime=");
    ret.append(String.valueOf(completionDateTime));
    ret.append(")");

    return ret.toString();
  }

}
