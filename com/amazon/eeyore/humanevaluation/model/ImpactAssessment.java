package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="ImpactAssessment")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ImpactAssessment extends java.lang.Object  {

  /**
   * Statically creates a builder instance for ImpactAssessment.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ImpactAssessment.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected int timesSeen;
    /**
     * Sets the value of the field "timesSeen" to be used for the constructed object.
     * @param timesSeen
     *   The value of the "timesSeen" field.
     * @return
     *   This builder.
     */
    public Builder withTimesSeen(int timesSeen) {
      this.timesSeen = timesSeen;
      return this;
    }

    protected int firstPartyAdvertisersCount;
    /**
     * Sets the value of the field "firstPartyAdvertisersCount" to be used for the constructed object.
     * @param firstPartyAdvertisersCount
     *   The value of the "firstPartyAdvertisersCount" field.
     * @return
     *   This builder.
     */
    public Builder withFirstPartyAdvertisersCount(int firstPartyAdvertisersCount) {
      this.firstPartyAdvertisersCount = firstPartyAdvertisersCount;
      return this;
    }

    protected ImpactedCreatives impactedCreatives;
    /**
     * Sets the value of the field "impactedCreatives" to be used for the constructed object.
     * @param impactedCreatives
     *   The value of the "impactedCreatives" field.
     * @return
     *   This builder.
     */
    public Builder withImpactedCreatives(ImpactedCreatives impactedCreatives) {
      this.impactedCreatives = impactedCreatives;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ImpactAssessment instance) {
      instance.setTimesSeen(this.timesSeen);
      instance.setFirstPartyAdvertisersCount(this.firstPartyAdvertisersCount);
      instance.setImpactedCreatives(this.impactedCreatives);
    }

    /**
     * Builds an instance of ImpactAssessment.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ImpactAssessment build() {
      ImpactAssessment instance = new ImpactAssessment();

      populate(instance);

      return instance;
    }
  };

  private int timesSeen;
  private int firstPartyAdvertisersCount;
  private ImpactedCreatives impactedCreatives;

  public int getTimesSeen() {
    return this.timesSeen;
  }

  public void setTimesSeen(int timesSeen) {
    this.timesSeen = timesSeen;
  }

  public int getFirstPartyAdvertisersCount() {
    return this.firstPartyAdvertisersCount;
  }

  public void setFirstPartyAdvertisersCount(int firstPartyAdvertisersCount) {
    this.firstPartyAdvertisersCount = firstPartyAdvertisersCount;
  }

  public ImpactedCreatives getImpactedCreatives() {
    return this.impactedCreatives;
  }

  public void setImpactedCreatives(ImpactedCreatives impactedCreatives) {
    this.impactedCreatives = impactedCreatives;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ImpactAssessment");

  /**
   * HashCode implementation for ImpactAssessment
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getTimesSeen(),
        getFirstPartyAdvertisersCount(),
        getImpactedCreatives());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ImpactAssessment
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ImpactAssessment)) {
      return false;
    }

    ImpactAssessment that = (ImpactAssessment) other;

    return
        Objects.equals(getTimesSeen(), that.getTimesSeen())
        && Objects.equals(getFirstPartyAdvertisersCount(), that.getFirstPartyAdvertisersCount())
        && Objects.equals(getImpactedCreatives(), that.getImpactedCreatives());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("ImpactAssessment(");

    ret.append("timesSeen=");
    ret.append(String.valueOf(timesSeen));
    ret.append(", ");

    ret.append("firstPartyAdvertisersCount=");
    ret.append(String.valueOf(firstPartyAdvertisersCount));
    ret.append(", ");

    ret.append("impactedCreatives=");
    ret.append(String.valueOf(impactedCreatives));
    ret.append(")");

    return ret.toString();
  }

}
