package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="RenderSummary")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class RenderSummary extends java.lang.Object  {

  /**
   * Statically creates a builder instance for RenderSummary.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of RenderSummary.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String renderId;
    /**
     * Sets the value of the field "renderId" to be used for the constructed object.
     * @param renderId
     *   The value of the "renderId" field.
     * @return
     *   This builder.
     */
    public Builder withRenderId(String renderId) {
      this.renderId = renderId;
      return this;
    }

    protected String startTimestamp;
    /**
     * Sets the value of the field "startTimestamp" to be used for the constructed object.
     * @param startTimestamp
     *   The value of the "startTimestamp" field.
     * @return
     *   This builder.
     */
    public Builder withStartTimestamp(String startTimestamp) {
      this.startTimestamp = startTimestamp;
      return this;
    }

    protected String renderType;
    /**
     * Sets the value of the field "renderType" to be used for the constructed object.
     * @param renderType
     *   The value of the "renderType" field.
     * @return
     *   This builder.
     */
    public Builder withRenderType(String renderType) {
      this.renderType = renderType;
      return this;
    }

    protected boolean hasLandingPage;
    /**
     * Sets the value of the field "hasLandingPage" to be used for the constructed object.
     * @param hasLandingPage
     *   The value of the "hasLandingPage" field.
     * @return
     *   This builder.
     */
    public Builder withHasLandingPage(boolean hasLandingPage) {
      this.hasLandingPage = hasLandingPage;
      return this;
    }

    protected String device;
    /**
     * Sets the value of the field "device" to be used for the constructed object.
     * @param device
     *   The value of the "device" field.
     * @return
     *   This builder.
     */
    public Builder withDevice(String device) {
      this.device = device;
      return this;
    }

    protected String operatingSystem;
    /**
     * Sets the value of the field "operatingSystem" to be used for the constructed object.
     * @param operatingSystem
     *   The value of the "operatingSystem" field.
     * @return
     *   This builder.
     */
    public Builder withOperatingSystem(String operatingSystem) {
      this.operatingSystem = operatingSystem;
      return this;
    }

    protected String browser;
    /**
     * Sets the value of the field "browser" to be used for the constructed object.
     * @param browser
     *   The value of the "browser" field.
     * @return
     *   This builder.
     */
    public Builder withBrowser(String browser) {
      this.browser = browser;
      return this;
    }

    protected String result;
    /**
     * Sets the value of the field "result" to be used for the constructed object.
     * @param result
     *   The value of the "result" field.
     * @return
     *   This builder.
     */
    public Builder withResult(String result) {
      this.result = result;
      return this;
    }

    protected boolean hasMalwareDetection;
    /**
     * Sets the value of the field "hasMalwareDetection" to be used for the constructed object.
     * @param hasMalwareDetection
     *   The value of the "hasMalwareDetection" field.
     * @return
     *   This builder.
     */
    public Builder withHasMalwareDetection(boolean hasMalwareDetection) {
      this.hasMalwareDetection = hasMalwareDetection;
      return this;
    }

    protected String imagePreviewUrl;
    /**
     * Sets the value of the field "imagePreviewUrl" to be used for the constructed object.
     * @param imagePreviewUrl
     *   The value of the "imagePreviewUrl" field.
     * @return
     *   This builder.
     */
    public Builder withImagePreviewUrl(String imagePreviewUrl) {
      this.imagePreviewUrl = imagePreviewUrl;
      return this;
    }

    protected boolean hasScreenCaptureFrames;
    /**
     * Sets the value of the field "hasScreenCaptureFrames" to be used for the constructed object.
     * @param hasScreenCaptureFrames
     *   The value of the "hasScreenCaptureFrames" field.
     * @return
     *   This builder.
     */
    public Builder withHasScreenCaptureFrames(boolean hasScreenCaptureFrames) {
      this.hasScreenCaptureFrames = hasScreenCaptureFrames;
      return this;
    }

    protected boolean hasScreenCaptureVideo;
    /**
     * Sets the value of the field "hasScreenCaptureVideo" to be used for the constructed object.
     * @param hasScreenCaptureVideo
     *   The value of the "hasScreenCaptureVideo" field.
     * @return
     *   This builder.
     */
    public Builder withHasScreenCaptureVideo(boolean hasScreenCaptureVideo) {
      this.hasScreenCaptureVideo = hasScreenCaptureVideo;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(RenderSummary instance) {
      instance.setRenderId(this.renderId);
      instance.setStartTimestamp(this.startTimestamp);
      instance.setRenderType(this.renderType);
      instance.setHasLandingPage(this.hasLandingPage);
      instance.setDevice(this.device);
      instance.setOperatingSystem(this.operatingSystem);
      instance.setBrowser(this.browser);
      instance.setResult(this.result);
      instance.setHasMalwareDetection(this.hasMalwareDetection);
      instance.setImagePreviewUrl(this.imagePreviewUrl);
      instance.setHasScreenCaptureFrames(this.hasScreenCaptureFrames);
      instance.setHasScreenCaptureVideo(this.hasScreenCaptureVideo);
    }

    /**
     * Builds an instance of RenderSummary.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public RenderSummary build() {
      RenderSummary instance = new RenderSummary();

      populate(instance);

      return instance;
    }
  };

  private String renderId;
  private String startTimestamp;
  private String renderType;
  private boolean hasLandingPage;
  private String device;
  private String operatingSystem;
  private String browser;
  private String result;
  private boolean hasMalwareDetection;
  private String imagePreviewUrl;
  private boolean hasScreenCaptureFrames;
  private boolean hasScreenCaptureVideo;

  @Required()
  public String getRenderId() {
    return this.renderId;
  }

  public void setRenderId(String renderId) {
    this.renderId = renderId;
  }

  @Required()
  public String getStartTimestamp() {
    return this.startTimestamp;
  }

  public void setStartTimestamp(String startTimestamp) {
    this.startTimestamp = startTimestamp;
  }

  @Required()
  public String getRenderType() {
    return this.renderType;
  }

  public void setRenderType(String renderType) {
    this.renderType = renderType;
  }

  @Required()
  public boolean isHasLandingPage() {
    return this.hasLandingPage;
  }

  public void setHasLandingPage(boolean hasLandingPage) {
    this.hasLandingPage = hasLandingPage;
  }

  public String getDevice() {
    return this.device;
  }

  public void setDevice(String device) {
    this.device = device;
  }

  @Required()
  public String getOperatingSystem() {
    return this.operatingSystem;
  }

  public void setOperatingSystem(String operatingSystem) {
    this.operatingSystem = operatingSystem;
  }

  @Required()
  public String getBrowser() {
    return this.browser;
  }

  public void setBrowser(String browser) {
    this.browser = browser;
  }

  @Required()
  public String getResult() {
    return this.result;
  }

  public void setResult(String result) {
    this.result = result;
  }

  @Required()
  public boolean isHasMalwareDetection() {
    return this.hasMalwareDetection;
  }

  public void setHasMalwareDetection(boolean hasMalwareDetection) {
    this.hasMalwareDetection = hasMalwareDetection;
  }

  public String getImagePreviewUrl() {
    return this.imagePreviewUrl;
  }

  public void setImagePreviewUrl(String imagePreviewUrl) {
    this.imagePreviewUrl = imagePreviewUrl;
  }

  public boolean isHasScreenCaptureFrames() {
    return this.hasScreenCaptureFrames;
  }

  public void setHasScreenCaptureFrames(boolean hasScreenCaptureFrames) {
    this.hasScreenCaptureFrames = hasScreenCaptureFrames;
  }

  public boolean isHasScreenCaptureVideo() {
    return this.hasScreenCaptureVideo;
  }

  public void setHasScreenCaptureVideo(boolean hasScreenCaptureVideo) {
    this.hasScreenCaptureVideo = hasScreenCaptureVideo;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.RenderSummary");

  /**
   * HashCode implementation for RenderSummary
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getRenderId(),
        getStartTimestamp(),
        getRenderType(),
        isHasLandingPage(),
        getDevice(),
        getOperatingSystem(),
        getBrowser(),
        getResult(),
        isHasMalwareDetection(),
        getImagePreviewUrl(),
        isHasScreenCaptureFrames(),
        isHasScreenCaptureVideo());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for RenderSummary
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof RenderSummary)) {
      return false;
    }

    RenderSummary that = (RenderSummary) other;

    return
        Objects.equals(getRenderId(), that.getRenderId())
        && Objects.equals(getStartTimestamp(), that.getStartTimestamp())
        && Objects.equals(getRenderType(), that.getRenderType())
        && Objects.equals(isHasLandingPage(), that.isHasLandingPage())
        && Objects.equals(getDevice(), that.getDevice())
        && Objects.equals(getOperatingSystem(), that.getOperatingSystem())
        && Objects.equals(getBrowser(), that.getBrowser())
        && Objects.equals(getResult(), that.getResult())
        && Objects.equals(isHasMalwareDetection(), that.isHasMalwareDetection())
        && Objects.equals(getImagePreviewUrl(), that.getImagePreviewUrl())
        && Objects.equals(isHasScreenCaptureFrames(), that.isHasScreenCaptureFrames())
        && Objects.equals(isHasScreenCaptureVideo(), that.isHasScreenCaptureVideo());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("RenderSummary(");

    ret.append("renderId=");
    ret.append(String.valueOf(renderId));
    ret.append(", ");

    ret.append("startTimestamp=");
    ret.append(String.valueOf(startTimestamp));
    ret.append(", ");

    ret.append("renderType=");
    ret.append(String.valueOf(renderType));
    ret.append(", ");

    ret.append("hasLandingPage=");
    ret.append(String.valueOf(hasLandingPage));
    ret.append(", ");

    ret.append("device=");
    ret.append(String.valueOf(device));
    ret.append(", ");

    ret.append("operatingSystem=");
    ret.append(String.valueOf(operatingSystem));
    ret.append(", ");

    ret.append("browser=");
    ret.append(String.valueOf(browser));
    ret.append(", ");

    ret.append("result=");
    ret.append(String.valueOf(result));
    ret.append(", ");

    ret.append("hasMalwareDetection=");
    ret.append(String.valueOf(hasMalwareDetection));
    ret.append(", ");

    ret.append("imagePreviewUrl=");
    ret.append(String.valueOf(imagePreviewUrl));
    ret.append(", ");

    ret.append("hasScreenCaptureFrames=");
    ret.append(String.valueOf(hasScreenCaptureFrames));
    ret.append(", ");

    ret.append("hasScreenCaptureVideo=");
    ret.append(String.valueOf(hasScreenCaptureVideo));
    ret.append(")");

    return ret.toString();
  }

}
