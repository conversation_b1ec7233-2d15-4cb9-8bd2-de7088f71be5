package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="ScatterPlotRequestedPlot")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ScatterPlotRequestedPlot extends java.lang.Object  {

  /**
   * Statically creates a builder instance for ScatterPlotRequestedPlot.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ScatterPlotRequestedPlot.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String outputLocation;
    /**
     * Sets the value of the field "outputLocation" to be used for the constructed object.
     * @param outputLocation
     *   The value of the "outputLocation" field.
     * @return
     *   This builder.
     */
    public Builder withOutputLocation(String outputLocation) {
      this.outputLocation = outputLocation;
      return this;
    }

    protected String requestStartTimestamp;
    /**
     * Sets the value of the field "requestStartTimestamp" to be used for the constructed object.
     * @param requestStartTimestamp
     *   The value of the "requestStartTimestamp" field.
     * @return
     *   This builder.
     */
    public Builder withRequestStartTimestamp(String requestStartTimestamp) {
      this.requestStartTimestamp = requestStartTimestamp;
      return this;
    }

    protected String user;
    /**
     * Sets the value of the field "user" to be used for the constructed object.
     * @param user
     *   The value of the "user" field.
     * @return
     *   This builder.
     */
    public Builder withUser(String user) {
      this.user = user;
      return this;
    }

    protected String tag;
    /**
     * Sets the value of the field "tag" to be used for the constructed object.
     * @param tag
     *   The value of the "tag" field.
     * @return
     *   This builder.
     */
    public Builder withTag(String tag) {
      this.tag = tag;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ScatterPlotRequestedPlot instance) {
      instance.setOutputLocation(this.outputLocation);
      instance.setRequestStartTimestamp(this.requestStartTimestamp);
      instance.setUser(this.user);
      instance.setTag(this.tag);
    }

    /**
     * Builds an instance of ScatterPlotRequestedPlot.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ScatterPlotRequestedPlot build() {
      ScatterPlotRequestedPlot instance = new ScatterPlotRequestedPlot();

      populate(instance);

      return instance;
    }
  };

  private String outputLocation;
  private String requestStartTimestamp;
  private String user;
  private String tag;

  @Required()
  public String getOutputLocation() {
    return this.outputLocation;
  }

  public void setOutputLocation(String outputLocation) {
    this.outputLocation = outputLocation;
  }

  @Required()
  public String getRequestStartTimestamp() {
    return this.requestStartTimestamp;
  }

  public void setRequestStartTimestamp(String requestStartTimestamp) {
    this.requestStartTimestamp = requestStartTimestamp;
  }

  @Required()
  public String getUser() {
    return this.user;
  }

  public void setUser(String user) {
    this.user = user;
  }

  @Required()
  public String getTag() {
    return this.tag;
  }

  public void setTag(String tag) {
    this.tag = tag;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ScatterPlotRequestedPlot");

  /**
   * HashCode implementation for ScatterPlotRequestedPlot
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getOutputLocation(),
        getRequestStartTimestamp(),
        getUser(),
        getTag());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ScatterPlotRequestedPlot
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ScatterPlotRequestedPlot)) {
      return false;
    }

    ScatterPlotRequestedPlot that = (ScatterPlotRequestedPlot) other;

    return
        Objects.equals(getOutputLocation(), that.getOutputLocation())
        && Objects.equals(getRequestStartTimestamp(), that.getRequestStartTimestamp())
        && Objects.equals(getUser(), that.getUser())
        && Objects.equals(getTag(), that.getTag());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("ScatterPlotRequestedPlot(");

    ret.append("outputLocation=");
    ret.append(String.valueOf(outputLocation));
    ret.append(", ");

    ret.append("requestStartTimestamp=");
    ret.append(String.valueOf(requestStartTimestamp));
    ret.append(", ");

    ret.append("user=");
    ret.append(String.valueOf(user));
    ret.append(", ");

    ret.append("tag=");
    ret.append(String.valueOf(tag));
    ret.append(")");

    return ret.toString();
  }

}
