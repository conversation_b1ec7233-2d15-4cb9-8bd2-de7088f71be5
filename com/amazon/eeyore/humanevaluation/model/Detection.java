package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.EnumValues;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.Map;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="Detection")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class Detection extends java.lang.Object  {

  /**
   * Statically creates a builder instance for Detection.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of Detection.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String detectionId;
    /**
     * Sets the value of the field "detectionId" to be used for the constructed object.
     * @param detectionId
     *   The value of the "detectionId" field.
     * @return
     *   This builder.
     */
    public Builder withDetectionId(String detectionId) {
      this.detectionId = detectionId;
      return this;
    }

    protected String detectionType;
    /**
     * Sets the value of the field "detectionType" to be used for the constructed object.
     * @param detectionType
     *   The value of the "detectionType" field.
     * @return
     *   This builder.
     */
    public Builder withDetectionType(String detectionType) {
      this.detectionType = detectionType;
      return this;
    }

    protected String eriskayId;
    /**
     * Sets the value of the field "eriskayId" to be used for the constructed object.
     * @param eriskayId
     *   The value of the "eriskayId" field.
     * @return
     *   This builder.
     */
    public Builder withEriskayId(String eriskayId) {
      this.eriskayId = eriskayId;
      return this;
    }

    protected String signatureId;
    /**
     * Sets the value of the field "signatureId" to be used for the constructed object.
     * @param signatureId
     *   The value of the "signatureId" field.
     * @return
     *   This builder.
     */
    public Builder withSignatureId(String signatureId) {
      this.signatureId = signatureId;
      return this;
    }

    protected String landingPage;
    /**
     * Sets the value of the field "landingPage" to be used for the constructed object.
     * @param landingPage
     *   The value of the "landingPage" field.
     * @return
     *   This builder.
     */
    public Builder withLandingPage(String landingPage) {
      this.landingPage = landingPage;
      return this;
    }

    protected String renderId;
    /**
     * Sets the value of the field "renderId" to be used for the constructed object.
     * @param renderId
     *   The value of the "renderId" field.
     * @return
     *   This builder.
     */
    public Builder withRenderId(String renderId) {
      this.renderId = renderId;
      return this;
    }

    protected String status;
    /**
     * Sets the value of the field "status" to be used for the constructed object.
     * @param status
     *   The value of the "status" field.
     * @return
     *   This builder.
     */
    public Builder withStatus(String status) {
      this.status = status;
      return this;
    }

    protected String creationTime;
    /**
     * Sets the value of the field "creationTime" to be used for the constructed object.
     * @param creationTime
     *   The value of the "creationTime" field.
     * @return
     *   This builder.
     */
    public Builder withCreationTime(String creationTime) {
      this.creationTime = creationTime;
      return this;
    }

    protected String lastUpdatedTime;
    /**
     * Sets the value of the field "lastUpdatedTime" to be used for the constructed object.
     * @param lastUpdatedTime
     *   The value of the "lastUpdatedTime" field.
     * @return
     *   This builder.
     */
    public Builder withLastUpdatedTime(String lastUpdatedTime) {
      this.lastUpdatedTime = lastUpdatedTime;
      return this;
    }

    protected String matchType;
    /**
     * Sets the value of the field "matchType" to be used for the constructed object.
     * @param matchType
     *   The value of the "matchType" field.
     * @return
     *   This builder.
     */
    public Builder withMatchType(String matchType) {
      this.matchType = matchType;
      return this;
    }

    protected String metadata;
    /**
     * Sets the value of the field "metadata" to be used for the constructed object.
     * @param metadata
     *   The value of the "metadata" field.
     * @return
     *   This builder.
     */
    public Builder withMetadata(String metadata) {
      this.metadata = metadata;
      return this;
    }

    protected Map<String, String> validationDetails;
    /**
     * Sets the value of the field "validationDetails" to be used for the constructed object.
     * @param validationDetails
     *   The value of the "validationDetails" field.
     * @return
     *   This builder.
     */
    public Builder withValidationDetails(Map<String, String> validationDetails) {
      this.validationDetails = validationDetails;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(Detection instance) {
      instance.setDetectionId(this.detectionId);
      instance.setDetectionType(this.detectionType);
      instance.setEriskayId(this.eriskayId);
      instance.setSignatureId(this.signatureId);
      instance.setLandingPage(this.landingPage);
      instance.setRenderId(this.renderId);
      instance.setStatus(this.status);
      instance.setCreationTime(this.creationTime);
      instance.setLastUpdatedTime(this.lastUpdatedTime);
      instance.setMatchType(this.matchType);
      instance.setMetadata(this.metadata);
      instance.setValidationDetails(this.validationDetails);
    }

    /**
     * Builds an instance of Detection.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public Detection build() {
      Detection instance = new Detection();

      populate(instance);

      return instance;
    }
  };

  private String detectionId;
  private String detectionType;
  private String eriskayId;
  private String signatureId;
  private String landingPage;
  private String renderId;
  private String status;
  private String creationTime;
  private String lastUpdatedTime;
  private String matchType;
  private String metadata;
  private Map<String, String> validationDetails;

  @Required()
  public String getDetectionId() {
    return this.detectionId;
  }

  public void setDetectionId(String detectionId) {
    this.detectionId = detectionId;
  }

  @EnumValues(value={"Simulated","Online"})
  @Required()
  public String getDetectionType() {
    return this.detectionType;
  }

  public void setDetectionType(String detectionType) {
    this.detectionType = detectionType;
  }

  public String getEriskayId() {
    return this.eriskayId;
  }

  public void setEriskayId(String eriskayId) {
    this.eriskayId = eriskayId;
  }

  @Required()
  public String getSignatureId() {
    return this.signatureId;
  }

  public void setSignatureId(String signatureId) {
    this.signatureId = signatureId;
  }

  public String getLandingPage() {
    return this.landingPage;
  }

  public void setLandingPage(String landingPage) {
    this.landingPage = landingPage;
  }

  public String getRenderId() {
    return this.renderId;
  }

  public void setRenderId(String renderId) {
    this.renderId = renderId;
  }

  @EnumValues(value={"PENDING_VERIFICATION","UNSAFE","FALSE_POSITIVE","SHADOW"})
  @Required()
  public String getStatus() {
    return this.status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  @Required()
  public String getCreationTime() {
    return this.creationTime;
  }

  public void setCreationTime(String creationTime) {
    this.creationTime = creationTime;
  }

  public String getLastUpdatedTime() {
    return this.lastUpdatedTime;
  }

  public void setLastUpdatedTime(String lastUpdatedTime) {
    this.lastUpdatedTime = lastUpdatedTime;
  }

  @EnumValues(value={"EXACT","SIMILARITY"})
  public String getMatchType() {
    return this.matchType;
  }

  public void setMatchType(String matchType) {
    this.matchType = matchType;
  }

  public String getMetadata() {
    return this.metadata;
  }

  public void setMetadata(String metadata) {
    this.metadata = metadata;
  }

  @MapKeyConstraint(@NestedConstraints())
  @MapValueConstraint(@NestedConstraints())
  public Map<String, String> getValidationDetails() {
    return this.validationDetails;
  }

  public void setValidationDetails(Map<String, String> validationDetails) {
    this.validationDetails = validationDetails;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.Detection");

  /**
   * HashCode implementation for Detection
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getDetectionId(),
        getDetectionType(),
        getEriskayId(),
        getSignatureId(),
        getLandingPage(),
        getRenderId(),
        getStatus(),
        getCreationTime(),
        getLastUpdatedTime(),
        getMatchType(),
        getMetadata(),
        getValidationDetails());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for Detection
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof Detection)) {
      return false;
    }

    Detection that = (Detection) other;

    return
        Objects.equals(getDetectionId(), that.getDetectionId())
        && Objects.equals(getDetectionType(), that.getDetectionType())
        && Objects.equals(getEriskayId(), that.getEriskayId())
        && Objects.equals(getSignatureId(), that.getSignatureId())
        && Objects.equals(getLandingPage(), that.getLandingPage())
        && Objects.equals(getRenderId(), that.getRenderId())
        && Objects.equals(getStatus(), that.getStatus())
        && Objects.equals(getCreationTime(), that.getCreationTime())
        && Objects.equals(getLastUpdatedTime(), that.getLastUpdatedTime())
        && Objects.equals(getMatchType(), that.getMatchType())
        && Objects.equals(getMetadata(), that.getMetadata())
        && Objects.equals(getValidationDetails(), that.getValidationDetails());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("Detection(");

    ret.append("detectionId=");
    ret.append(String.valueOf(detectionId));
    ret.append(", ");

    ret.append("detectionType=");
    ret.append(String.valueOf(detectionType));
    ret.append(", ");

    ret.append("eriskayId=");
    ret.append(String.valueOf(eriskayId));
    ret.append(", ");

    ret.append("signatureId=");
    ret.append(String.valueOf(signatureId));
    ret.append(", ");

    ret.append("landingPage=");
    ret.append(String.valueOf(landingPage));
    ret.append(", ");

    ret.append("renderId=");
    ret.append(String.valueOf(renderId));
    ret.append(", ");

    ret.append("status=");
    ret.append(String.valueOf(status));
    ret.append(", ");

    ret.append("creationTime=");
    ret.append(String.valueOf(creationTime));
    ret.append(", ");

    ret.append("lastUpdatedTime=");
    ret.append(String.valueOf(lastUpdatedTime));
    ret.append(", ");

    ret.append("matchType=");
    ret.append(String.valueOf(matchType));
    ret.append(", ");

    ret.append("metadata=");
    ret.append(String.valueOf(metadata));
    ret.append(", ");

    ret.append("validationDetails=");
    ret.append(String.valueOf(validationDetails));
    ret.append(")");

    return ret.toString();
  }

}
