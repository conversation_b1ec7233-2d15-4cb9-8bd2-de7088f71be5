package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="MalwareDetectionAnnotation")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class MalwareDetectionAnnotation extends java.lang.Object  {

  /**
   * Statically creates a builder instance for MalwareDetectionAnnotation.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of MalwareDetectionAnnotation.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String incidentID;
    /**
     * Sets the value of the field "incidentID" to be used for the constructed object.
     * @param incidentID
     *   The value of the "incidentID" field.
     * @return
     *   This builder.
     */
    public Builder withIncidentID(String incidentID) {
      this.incidentID = incidentID;
      return this;
    }

    protected String description;
    /**
     * Sets the value of the field "description" to be used for the constructed object.
     * @param description
     *   The value of the "description" field.
     * @return
     *   This builder.
     */
    public Builder withDescription(String description) {
      this.description = description;
      return this;
    }

    protected String detectionSourceSystem;
    /**
     * Sets the value of the field "detectionSourceSystem" to be used for the constructed object.
     * @param detectionSourceSystem
     *   The value of the "detectionSourceSystem" field.
     * @return
     *   This builder.
     */
    public Builder withDetectionSourceSystem(String detectionSourceSystem) {
      this.detectionSourceSystem = detectionSourceSystem;
      return this;
    }

    protected String enforcementType;
    /**
     * Sets the value of the field "enforcementType" to be used for the constructed object.
     * @param enforcementType
     *   The value of the "enforcementType" field.
     * @return
     *   This builder.
     */
    public Builder withEnforcementType(String enforcementType) {
      this.enforcementType = enforcementType;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(MalwareDetectionAnnotation instance) {
      instance.setIncidentID(this.incidentID);
      instance.setDescription(this.description);
      instance.setDetectionSourceSystem(this.detectionSourceSystem);
      instance.setEnforcementType(this.enforcementType);
    }

    /**
     * Builds an instance of MalwareDetectionAnnotation.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public MalwareDetectionAnnotation build() {
      MalwareDetectionAnnotation instance = new MalwareDetectionAnnotation();

      populate(instance);

      return instance;
    }
  };

  private String incidentID;
  private String description;
  private String detectionSourceSystem;
  private String enforcementType;

  @Required()
  public String getIncidentID() {
    return this.incidentID;
  }

  public void setIncidentID(String incidentID) {
    this.incidentID = incidentID;
  }

  @Required()
  public String getDescription() {
    return this.description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  @Required()
  public String getDetectionSourceSystem() {
    return this.detectionSourceSystem;
  }

  public void setDetectionSourceSystem(String detectionSourceSystem) {
    this.detectionSourceSystem = detectionSourceSystem;
  }

  @Required()
  public String getEnforcementType() {
    return this.enforcementType;
  }

  public void setEnforcementType(String enforcementType) {
    this.enforcementType = enforcementType;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.MalwareDetectionAnnotation");

  /**
   * HashCode implementation for MalwareDetectionAnnotation
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getIncidentID(),
        getDescription(),
        getDetectionSourceSystem(),
        getEnforcementType());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for MalwareDetectionAnnotation
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof MalwareDetectionAnnotation)) {
      return false;
    }

    MalwareDetectionAnnotation that = (MalwareDetectionAnnotation) other;

    return
        Objects.equals(getIncidentID(), that.getIncidentID())
        && Objects.equals(getDescription(), that.getDescription())
        && Objects.equals(getDetectionSourceSystem(), that.getDetectionSourceSystem())
        && Objects.equals(getEnforcementType(), that.getEnforcementType());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("MalwareDetectionAnnotation(");

    ret.append("incidentID=");
    ret.append(String.valueOf(incidentID));
    ret.append(", ");

    ret.append("description=");
    ret.append(String.valueOf(description));
    ret.append(", ");

    ret.append("detectionSourceSystem=");
    ret.append(String.valueOf(detectionSourceSystem));
    ret.append(", ");

    ret.append("enforcementType=");
    ret.append(String.valueOf(enforcementType));
    ret.append(")");

    return ret.toString();
  }

}
