package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.EnumValues;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="ScreencastFrame")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class Screencast<PERSON>rame extends java.lang.Object  {

  /**
   * Statically creates a builder instance for ScreencastFrame.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ScreencastFrame.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String id;
    /**
     * Sets the value of the field "id" to be used for the constructed object.
     * @param id
     *   The value of the "id" field.
     * @return
     *   This builder.
     */
    public Builder withId(String id) {
      this.id = id;
      return this;
    }

    protected String url;
    /**
     * Sets the value of the field "url" to be used for the constructed object.
     * @param url
     *   The value of the "url" field.
     * @return
     *   This builder.
     */
    public Builder withUrl(String url) {
      this.url = url;
      return this;
    }

    protected String renderPartition;
    /**
     * Sets the value of the field "renderPartition" to be used for the constructed object.
     * @param renderPartition
     *   The value of the "renderPartition" field.
     * @return
     *   This builder.
     */
    public Builder withRenderPartition(String renderPartition) {
      this.renderPartition = renderPartition;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ScreencastFrame instance) {
      instance.setId(this.id);
      instance.setUrl(this.url);
      instance.setRenderPartition(this.renderPartition);
    }

    /**
     * Builds an instance of ScreencastFrame.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ScreencastFrame build() {
      ScreencastFrame instance = new ScreencastFrame();

      populate(instance);

      return instance;
    }
  };

  private String id;
  private String url;
  private String renderPartition;

  @Required()
  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  @Required()
  public String getUrl() {
    return this.url;
  }

  public void setUrl(String url) {
    this.url = url;
  }

  @Required()
  @EnumValues(value={"creative","landingPage"})
  public String getRenderPartition() {
    return this.renderPartition;
  }

  public void setRenderPartition(String renderPartition) {
    this.renderPartition = renderPartition;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ScreencastFrame");

  /**
   * HashCode implementation for ScreencastFrame
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getId(),
        getUrl(),
        getRenderPartition());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ScreencastFrame
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ScreencastFrame)) {
      return false;
    }

    ScreencastFrame that = (ScreencastFrame) other;

    return
        Objects.equals(getId(), that.getId())
        && Objects.equals(getUrl(), that.getUrl())
        && Objects.equals(getRenderPartition(), that.getRenderPartition());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("ScreencastFrame(");

    ret.append("id=");
    ret.append(String.valueOf(id));
    ret.append(", ");

    ret.append("url=");
    ret.append(String.valueOf(url));
    ret.append(", ");

    ret.append("renderPartition=");
    ret.append(String.valueOf(renderPartition));
    ret.append(")");

    return ret.toString();
  }

}
