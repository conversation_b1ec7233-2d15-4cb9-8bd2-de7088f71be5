package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.EnumValues;
import com.amazon.coral.annotation.HttpLabel;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="SearchSignaturesRequest")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class SearchSignaturesRequest extends java.lang.Object  {

  /**
   * Statically creates a builder instance for SearchSignaturesRequest.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of SearchSignaturesRequest.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String status;
    /**
     * Sets the value of the field "status" to be used for the constructed object.
     * @param status
     *   The value of the "status" field.
     * @return
     *   This builder.
     */
    public Builder withStatus(String status) {
      this.status = status;
      return this;
    }

    protected int maxResults;
    /**
     * Sets the value of the field "maxResults" to be used for the constructed object.
     * @param maxResults
     *   The value of the "maxResults" field.
     * @return
     *   This builder.
     */
    public Builder withMaxResults(int maxResults) {
      this.maxResults = maxResults;
      return this;
    }

    protected String nextToken;
    /**
     * Sets the value of the field "nextToken" to be used for the constructed object.
     * @param nextToken
     *   The value of the "nextToken" field.
     * @return
     *   This builder.
     */
    public Builder withNextToken(String nextToken) {
      this.nextToken = nextToken;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(SearchSignaturesRequest instance) {
      instance.setStatus(this.status);
      instance.setMaxResults(this.maxResults);
      instance.setNextToken(this.nextToken);
    }

    /**
     * Builds an instance of SearchSignaturesRequest.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public SearchSignaturesRequest build() {
      SearchSignaturesRequest instance = new SearchSignaturesRequest();

      populate(instance);

      return instance;
    }
  };

  private String status;
  private int maxResults;
  private String nextToken;

  @EnumValues(value={"SHADOW","LIVE","DELETED","PROPOSED","PENDING_AUTO_VERIFICATION","REJECTED","PENDING_MANUAL_REVIEW","LIVE_REFERRAL"})
  @HttpLabel(value="status")
  @Required()
  public String getStatus() {
    return this.status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  @HttpLabel(value="maxResults")
  public int getMaxResults() {
    return this.maxResults;
  }

  public void setMaxResults(int maxResults) {
    this.maxResults = maxResults;
  }

  @HttpLabel(value="nextToken")
  public String getNextToken() {
    return this.nextToken;
  }

  public void setNextToken(String nextToken) {
    this.nextToken = nextToken;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.SearchSignaturesRequest");

  /**
   * HashCode implementation for SearchSignaturesRequest
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getStatus(),
        getMaxResults(),
        getNextToken());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for SearchSignaturesRequest
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof SearchSignaturesRequest)) {
      return false;
    }

    SearchSignaturesRequest that = (SearchSignaturesRequest) other;

    return
        Objects.equals(getStatus(), that.getStatus())
        && Objects.equals(getMaxResults(), that.getMaxResults())
        && Objects.equals(getNextToken(), that.getNextToken());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("SearchSignaturesRequest(");

    ret.append("status=");
    ret.append(String.valueOf(status));
    ret.append(", ");

    ret.append("maxResults=");
    ret.append(String.valueOf(maxResults));
    ret.append(", ");

    ret.append("nextToken=");
    ret.append(String.valueOf(nextToken));
    ret.append(")");

    return ret.toString();
  }

}
