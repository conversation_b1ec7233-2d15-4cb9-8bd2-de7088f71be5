package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="StandardRenderArtifacts")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public abstract class StandardRenderArtifacts extends java.lang.Object  {

  /**
   * Fluent builder for instances of StandardRenderArtifacts.
   */
  @com.amazon.coral.annotation.Generated
  public static abstract class Builder {

    protected List<Screenshot> screenshots;
    /**
     * Sets the value of the field "screenshots" to be used for the constructed object.
     * @param screenshots
     *   The value of the "screenshots" field.
     * @return
     *   This builder.
     */
    public Builder withScreenshots(List<Screenshot> screenshots) {
      this.screenshots = screenshots;
      return this;
    }

    protected List<ScreencastFrame> screencastFrames;
    /**
     * Sets the value of the field "screencastFrames" to be used for the constructed object.
     * @param screencastFrames
     *   The value of the "screencastFrames" field.
     * @return
     *   This builder.
     */
    public Builder withScreencastFrames(List<ScreencastFrame> screencastFrames) {
      this.screencastFrames = screencastFrames;
      return this;
    }

    protected List<ScreenCaptureVideo> screenCaptureVideos;
    /**
     * Sets the value of the field "screenCaptureVideos" to be used for the constructed object.
     * @param screenCaptureVideos
     *   The value of the "screenCaptureVideos" field.
     * @return
     *   This builder.
     */
    public Builder withScreenCaptureVideos(List<ScreenCaptureVideo> screenCaptureVideos) {
      this.screenCaptureVideos = screenCaptureVideos;
      return this;
    }

    protected String consoleLogLinesLocation;
    /**
     * Sets the value of the field "consoleLogLinesLocation" to be used for the constructed object.
     * @param consoleLogLinesLocation
     *   The value of the "consoleLogLinesLocation" field.
     * @return
     *   This builder.
     */
    public Builder withConsoleLogLinesLocation(String consoleLogLinesLocation) {
      this.consoleLogLinesLocation = consoleLogLinesLocation;
      return this;
    }

    protected String networkTraceLocation;
    /**
     * Sets the value of the field "networkTraceLocation" to be used for the constructed object.
     * @param networkTraceLocation
     *   The value of the "networkTraceLocation" field.
     * @return
     *   This builder.
     */
    public Builder withNetworkTraceLocation(String networkTraceLocation) {
      this.networkTraceLocation = networkTraceLocation;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(StandardRenderArtifacts instance) {
      instance.setScreenshots(this.screenshots);
      instance.setScreencastFrames(this.screencastFrames);
      instance.setScreenCaptureVideos(this.screenCaptureVideos);
      instance.setConsoleLogLinesLocation(this.consoleLogLinesLocation);
      instance.setNetworkTraceLocation(this.networkTraceLocation);
    }
  };

  private List<Screenshot> screenshots;
  private List<ScreencastFrame> screencastFrames;
  private List<ScreenCaptureVideo> screenCaptureVideos;
  private String consoleLogLinesLocation;
  private String networkTraceLocation;

  public List<Screenshot> getScreenshots() {
    return this.screenshots;
  }

  public void setScreenshots(List<Screenshot> screenshots) {
    this.screenshots = screenshots;
  }

  public List<ScreencastFrame> getScreencastFrames() {
    return this.screencastFrames;
  }

  public void setScreencastFrames(List<ScreencastFrame> screencastFrames) {
    this.screencastFrames = screencastFrames;
  }

  public List<ScreenCaptureVideo> getScreenCaptureVideos() {
    return this.screenCaptureVideos;
  }

  public void setScreenCaptureVideos(List<ScreenCaptureVideo> screenCaptureVideos) {
    this.screenCaptureVideos = screenCaptureVideos;
  }

  public String getConsoleLogLinesLocation() {
    return this.consoleLogLinesLocation;
  }

  public void setConsoleLogLinesLocation(String consoleLogLinesLocation) {
    this.consoleLogLinesLocation = consoleLogLinesLocation;
  }

  public String getNetworkTraceLocation() {
    return this.networkTraceLocation;
  }

  public void setNetworkTraceLocation(String networkTraceLocation) {
    this.networkTraceLocation = networkTraceLocation;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.StandardRenderArtifacts");

  /**
   * HashCode implementation for StandardRenderArtifacts
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getScreenshots(),
        getScreencastFrames(),
        getScreenCaptureVideos(),
        getConsoleLogLinesLocation(),
        getNetworkTraceLocation());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for StandardRenderArtifacts
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof StandardRenderArtifacts)) {
      return false;
    }

    StandardRenderArtifacts that = (StandardRenderArtifacts) other;

    return
        Objects.equals(getScreenshots(), that.getScreenshots())
        && Objects.equals(getScreencastFrames(), that.getScreencastFrames())
        && Objects.equals(getScreenCaptureVideos(), that.getScreenCaptureVideos())
        && Objects.equals(getConsoleLogLinesLocation(), that.getConsoleLogLinesLocation())
        && Objects.equals(getNetworkTraceLocation(), that.getNetworkTraceLocation());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("StandardRenderArtifacts(");

    ret.append("screenshots=");
    ret.append(String.valueOf(screenshots));
    ret.append(", ");

    ret.append("screencastFrames=");
    ret.append(String.valueOf(screencastFrames));
    ret.append(", ");

    ret.append("screenCaptureVideos=");
    ret.append(String.valueOf(screenCaptureVideos));
    ret.append(", ");

    ret.append("consoleLogLinesLocation=");
    ret.append(String.valueOf(consoleLogLinesLocation));
    ret.append(", ");

    ret.append("networkTraceLocation=");
    ret.append(String.valueOf(networkTraceLocation));
    ret.append(")");

    return ret.toString();
  }

}
