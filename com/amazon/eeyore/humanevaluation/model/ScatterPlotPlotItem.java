package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="ScatterPlotPlotItem")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ScatterPlotPlotItem extends java.lang.Object  {

  /**
   * Statically creates a builder instance for ScatterPlotPlotItem.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ScatterPlotPlotItem.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String creativeID;
    /**
     * Sets the value of the field "creativeID" to be used for the constructed object.
     * @param creativeID
     *   The value of the "creativeID" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeID(String creativeID) {
      this.creativeID = creativeID;
      return this;
    }

    protected String creativeIDSpace;
    /**
     * Sets the value of the field "creativeIDSpace" to be used for the constructed object.
     * @param creativeIDSpace
     *   The value of the "creativeIDSpace" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeIDSpace(String creativeIDSpace) {
      this.creativeIDSpace = creativeIDSpace;
      return this;
    }

    protected String renderID;
    /**
     * Sets the value of the field "renderID" to be used for the constructed object.
     * @param renderID
     *   The value of the "renderID" field.
     * @return
     *   This builder.
     */
    public Builder withRenderID(String renderID) {
      this.renderID = renderID;
      return this;
    }

    protected float x;
    /**
     * Sets the value of the field "x" to be used for the constructed object.
     * @param x
     *   The value of the "x" field.
     * @return
     *   This builder.
     */
    public Builder withX(float x) {
      this.x = x;
      return this;
    }

    protected float y;
    /**
     * Sets the value of the field "y" to be used for the constructed object.
     * @param y
     *   The value of the "y" field.
     * @return
     *   This builder.
     */
    public Builder withY(float y) {
      this.y = y;
      return this;
    }

    protected String smallImage;
    /**
     * Sets the value of the field "smallImage" to be used for the constructed object.
     * @param smallImage
     *   The value of the "smallImage" field.
     * @return
     *   This builder.
     */
    public Builder withSmallImage(String smallImage) {
      this.smallImage = smallImage;
      return this;
    }

    protected String largeImage;
    /**
     * Sets the value of the field "largeImage" to be used for the constructed object.
     * @param largeImage
     *   The value of the "largeImage" field.
     * @return
     *   This builder.
     */
    public Builder withLargeImage(String largeImage) {
      this.largeImage = largeImage;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ScatterPlotPlotItem instance) {
      instance.setCreativeID(this.creativeID);
      instance.setCreativeIDSpace(this.creativeIDSpace);
      instance.setRenderID(this.renderID);
      instance.setX(this.x);
      instance.setY(this.y);
      instance.setSmallImage(this.smallImage);
      instance.setLargeImage(this.largeImage);
    }

    /**
     * Builds an instance of ScatterPlotPlotItem.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ScatterPlotPlotItem build() {
      ScatterPlotPlotItem instance = new ScatterPlotPlotItem();

      populate(instance);

      return instance;
    }
  };

  private String creativeID;
  private String creativeIDSpace;
  private String renderID;
  private float x;
  private float y;
  private String smallImage;
  private String largeImage;

  @Required()
  public String getCreativeID() {
    return this.creativeID;
  }

  public void setCreativeID(String creativeID) {
    this.creativeID = creativeID;
  }

  @Required()
  public String getCreativeIDSpace() {
    return this.creativeIDSpace;
  }

  public void setCreativeIDSpace(String creativeIDSpace) {
    this.creativeIDSpace = creativeIDSpace;
  }

  @Required()
  public String getRenderID() {
    return this.renderID;
  }

  public void setRenderID(String renderID) {
    this.renderID = renderID;
  }

  @Required()
  public float getX() {
    return this.x;
  }

  public void setX(float x) {
    this.x = x;
  }

  @Required()
  public float getY() {
    return this.y;
  }

  public void setY(float y) {
    this.y = y;
  }

  @Required()
  public String getSmallImage() {
    return this.smallImage;
  }

  public void setSmallImage(String smallImage) {
    this.smallImage = smallImage;
  }

  @Required()
  public String getLargeImage() {
    return this.largeImage;
  }

  public void setLargeImage(String largeImage) {
    this.largeImage = largeImage;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ScatterPlotPlotItem");

  /**
   * HashCode implementation for ScatterPlotPlotItem
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getCreativeID(),
        getCreativeIDSpace(),
        getRenderID(),
        getX(),
        getY(),
        getSmallImage(),
        getLargeImage());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ScatterPlotPlotItem
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ScatterPlotPlotItem)) {
      return false;
    }

    ScatterPlotPlotItem that = (ScatterPlotPlotItem) other;

    return
        Objects.equals(getCreativeID(), that.getCreativeID())
        && Objects.equals(getCreativeIDSpace(), that.getCreativeIDSpace())
        && Objects.equals(getRenderID(), that.getRenderID())
        && Objects.equals(getX(), that.getX())
        && Objects.equals(getY(), that.getY())
        && Objects.equals(getSmallImage(), that.getSmallImage())
        && Objects.equals(getLargeImage(), that.getLargeImage());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("ScatterPlotPlotItem(");

    ret.append("creativeID=");
    ret.append(String.valueOf(creativeID));
    ret.append(", ");

    ret.append("creativeIDSpace=");
    ret.append(String.valueOf(creativeIDSpace));
    ret.append(", ");

    ret.append("renderID=");
    ret.append(String.valueOf(renderID));
    ret.append(", ");

    ret.append("x=");
    ret.append(String.valueOf(x));
    ret.append(", ");

    ret.append("y=");
    ret.append(String.valueOf(y));
    ret.append(", ");

    ret.append("smallImage=");
    ret.append(String.valueOf(smallImage));
    ret.append(", ");

    ret.append("largeImage=");
    ret.append(String.valueOf(largeImage));
    ret.append(")");

    return ret.toString();
  }

}
