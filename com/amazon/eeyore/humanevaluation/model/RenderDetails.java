package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="RenderDetails")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class RenderDetails extends java.lang.Object  {

  /**
   * Statically creates a builder instance for RenderDetails.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of RenderDetails.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String renderID;
    /**
     * Sets the value of the field "renderID" to be used for the constructed object.
     * @param renderID
     *   The value of the "renderID" field.
     * @return
     *   This builder.
     */
    public Builder withRenderID(String renderID) {
      this.renderID = renderID;
      return this;
    }

    protected String startTime;
    /**
     * Sets the value of the field "startTime" to be used for the constructed object.
     * @param startTime
     *   The value of the "startTime" field.
     * @return
     *   This builder.
     */
    public Builder withStartTime(String startTime) {
      this.startTime = startTime;
      return this;
    }

    protected String completionTime;
    /**
     * Sets the value of the field "completionTime" to be used for the constructed object.
     * @param completionTime
     *   The value of the "completionTime" field.
     * @return
     *   This builder.
     */
    public Builder withCompletionTime(String completionTime) {
      this.completionTime = completionTime;
      return this;
    }

    protected String renderType;
    /**
     * Sets the value of the field "renderType" to be used for the constructed object.
     * @param renderType
     *   The value of the "renderType" field.
     * @return
     *   This builder.
     */
    public Builder withRenderType(String renderType) {
      this.renderType = renderType;
      return this;
    }

    protected String fidelity;
    /**
     * Sets the value of the field "fidelity" to be used for the constructed object.
     * @param fidelity
     *   The value of the "fidelity" field.
     * @return
     *   This builder.
     */
    public Builder withFidelity(String fidelity) {
      this.fidelity = fidelity;
      return this;
    }

    protected String result;
    /**
     * Sets the value of the field "result" to be used for the constructed object.
     * @param result
     *   The value of the "result" field.
     * @return
     *   This builder.
     */
    public Builder withResult(String result) {
      this.result = result;
      return this;
    }

    protected RenderCreativeConfiguration creativeConfiguration;
    /**
     * Sets the value of the field "creativeConfiguration" to be used for the constructed object.
     * @param creativeConfiguration
     *   The value of the "creativeConfiguration" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeConfiguration(RenderCreativeConfiguration creativeConfiguration) {
      this.creativeConfiguration = creativeConfiguration;
      return this;
    }

    protected RenderTargetingConfiguration targetingConfiguration;
    /**
     * Sets the value of the field "targetingConfiguration" to be used for the constructed object.
     * @param targetingConfiguration
     *   The value of the "targetingConfiguration" field.
     * @return
     *   This builder.
     */
    public Builder withTargetingConfiguration(RenderTargetingConfiguration targetingConfiguration) {
      this.targetingConfiguration = targetingConfiguration;
      return this;
    }

    protected RenderClickConfiguration clickConfiguration;
    /**
     * Sets the value of the field "clickConfiguration" to be used for the constructed object.
     * @param clickConfiguration
     *   The value of the "clickConfiguration" field.
     * @return
     *   This builder.
     */
    public Builder withClickConfiguration(RenderClickConfiguration clickConfiguration) {
      this.clickConfiguration = clickConfiguration;
      return this;
    }

    protected RenderScreenCaptureConfiguration screenCaptureConfiguration;
    /**
     * Sets the value of the field "screenCaptureConfiguration" to be used for the constructed object.
     * @param screenCaptureConfiguration
     *   The value of the "screenCaptureConfiguration" field.
     * @return
     *   This builder.
     */
    public Builder withScreenCaptureConfiguration(RenderScreenCaptureConfiguration screenCaptureConfiguration) {
      this.screenCaptureConfiguration = screenCaptureConfiguration;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(RenderDetails instance) {
      instance.setRenderID(this.renderID);
      instance.setStartTime(this.startTime);
      instance.setCompletionTime(this.completionTime);
      instance.setRenderType(this.renderType);
      instance.setFidelity(this.fidelity);
      instance.setResult(this.result);
      instance.setCreativeConfiguration(this.creativeConfiguration);
      instance.setTargetingConfiguration(this.targetingConfiguration);
      instance.setClickConfiguration(this.clickConfiguration);
      instance.setScreenCaptureConfiguration(this.screenCaptureConfiguration);
    }

    /**
     * Builds an instance of RenderDetails.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public RenderDetails build() {
      RenderDetails instance = new RenderDetails();

      populate(instance);

      return instance;
    }
  };

  private String renderID;
  private String startTime;
  private String completionTime;
  private String renderType;
  private String fidelity;
  private String result;
  private RenderCreativeConfiguration creativeConfiguration;
  private RenderTargetingConfiguration targetingConfiguration;
  private RenderClickConfiguration clickConfiguration;
  private RenderScreenCaptureConfiguration screenCaptureConfiguration;

  @Required()
  public String getRenderID() {
    return this.renderID;
  }

  public void setRenderID(String renderID) {
    this.renderID = renderID;
  }

  @Required()
  public String getStartTime() {
    return this.startTime;
  }

  public void setStartTime(String startTime) {
    this.startTime = startTime;
  }

  @Required()
  public String getCompletionTime() {
    return this.completionTime;
  }

  public void setCompletionTime(String completionTime) {
    this.completionTime = completionTime;
  }

  @Required()
  public String getRenderType() {
    return this.renderType;
  }

  public void setRenderType(String renderType) {
    this.renderType = renderType;
  }

  @Required()
  public String getFidelity() {
    return this.fidelity;
  }

  public void setFidelity(String fidelity) {
    this.fidelity = fidelity;
  }

  @Required()
  public String getResult() {
    return this.result;
  }

  public void setResult(String result) {
    this.result = result;
  }

  @Required()
  public RenderCreativeConfiguration getCreativeConfiguration() {
    return this.creativeConfiguration;
  }

  public void setCreativeConfiguration(RenderCreativeConfiguration creativeConfiguration) {
    this.creativeConfiguration = creativeConfiguration;
  }

  @Required()
  public RenderTargetingConfiguration getTargetingConfiguration() {
    return this.targetingConfiguration;
  }

  public void setTargetingConfiguration(RenderTargetingConfiguration targetingConfiguration) {
    this.targetingConfiguration = targetingConfiguration;
  }

  public RenderClickConfiguration getClickConfiguration() {
    return this.clickConfiguration;
  }

  public void setClickConfiguration(RenderClickConfiguration clickConfiguration) {
    this.clickConfiguration = clickConfiguration;
  }

  public RenderScreenCaptureConfiguration getScreenCaptureConfiguration() {
    return this.screenCaptureConfiguration;
  }

  public void setScreenCaptureConfiguration(RenderScreenCaptureConfiguration screenCaptureConfiguration) {
    this.screenCaptureConfiguration = screenCaptureConfiguration;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.RenderDetails");

  /**
   * HashCode implementation for RenderDetails
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getRenderID(),
        getStartTime(),
        getCompletionTime(),
        getRenderType(),
        getFidelity(),
        getResult(),
        getCreativeConfiguration(),
        getTargetingConfiguration(),
        getClickConfiguration(),
        getScreenCaptureConfiguration());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for RenderDetails
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof RenderDetails)) {
      return false;
    }

    RenderDetails that = (RenderDetails) other;

    return
        Objects.equals(getRenderID(), that.getRenderID())
        && Objects.equals(getStartTime(), that.getStartTime())
        && Objects.equals(getCompletionTime(), that.getCompletionTime())
        && Objects.equals(getRenderType(), that.getRenderType())
        && Objects.equals(getFidelity(), that.getFidelity())
        && Objects.equals(getResult(), that.getResult())
        && Objects.equals(getCreativeConfiguration(), that.getCreativeConfiguration())
        && Objects.equals(getTargetingConfiguration(), that.getTargetingConfiguration())
        && Objects.equals(getClickConfiguration(), that.getClickConfiguration())
        && Objects.equals(getScreenCaptureConfiguration(), that.getScreenCaptureConfiguration());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("RenderDetails(");

    ret.append("renderID=");
    ret.append(String.valueOf(renderID));
    ret.append(", ");

    ret.append("startTime=");
    ret.append(String.valueOf(startTime));
    ret.append(", ");

    ret.append("completionTime=");
    ret.append(String.valueOf(completionTime));
    ret.append(", ");

    ret.append("renderType=");
    ret.append(String.valueOf(renderType));
    ret.append(", ");

    ret.append("fidelity=");
    ret.append(String.valueOf(fidelity));
    ret.append(", ");

    ret.append("result=");
    ret.append(String.valueOf(result));
    ret.append(", ");

    ret.append("creativeConfiguration=");
    ret.append(String.valueOf(creativeConfiguration));
    ret.append(", ");

    ret.append("targetingConfiguration=");
    ret.append(String.valueOf(targetingConfiguration));
    ret.append(", ");

    ret.append("clickConfiguration=");
    ret.append(String.valueOf(clickConfiguration));
    ret.append(", ");

    ret.append("screenCaptureConfiguration=");
    ret.append(String.valueOf(screenCaptureConfiguration));
    ret.append(")");

    return ret.toString();
  }

}
