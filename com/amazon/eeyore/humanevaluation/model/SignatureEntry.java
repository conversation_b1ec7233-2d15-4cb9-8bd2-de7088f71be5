package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.EnumValues;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="SignatureEntry")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class SignatureEntry extends java.lang.Object  {

  /**
   * Statically creates a builder instance for SignatureEntry.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of SignatureEntry.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String domain;
    /**
     * Sets the value of the field "domain" to be used for the constructed object.
     * @param domain
     *   The value of the "domain" field.
     * @return
     *   This builder.
     */
    public Builder withDomain(String domain) {
      this.domain = domain;
      return this;
    }

    protected String source;
    /**
     * Sets the value of the field "source" to be used for the constructed object.
     * @param source
     *   The value of the "source" field.
     * @return
     *   This builder.
     */
    public Builder withSource(String source) {
      this.source = source;
      return this;
    }

    protected String description;
    /**
     * Sets the value of the field "description" to be used for the constructed object.
     * @param description
     *   The value of the "description" field.
     * @return
     *   This builder.
     */
    public Builder withDescription(String description) {
      this.description = description;
      return this;
    }

    protected String status;
    /**
     * Sets the value of the field "status" to be used for the constructed object.
     * @param status
     *   The value of the "status" field.
     * @return
     *   This builder.
     */
    public Builder withStatus(String status) {
      this.status = status;
      return this;
    }

    protected String decision;
    /**
     * Sets the value of the field "decision" to be used for the constructed object.
     * @param decision
     *   The value of the "decision" field.
     * @return
     *   This builder.
     */
    public Builder withDecision(String decision) {
      this.decision = decision;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(SignatureEntry instance) {
      instance.setDomain(this.domain);
      instance.setSource(this.source);
      instance.setDescription(this.description);
      instance.setStatus(this.status);
      instance.setDecision(this.decision);
    }

    /**
     * Builds an instance of SignatureEntry.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public SignatureEntry build() {
      SignatureEntry instance = new SignatureEntry();

      populate(instance);

      return instance;
    }
  };

  private String domain;
  private String source;
  private String description;
  private String status;
  private String decision;

  @Required()
  public String getDomain() {
    return this.domain;
  }

  public void setDomain(String domain) {
    this.domain = domain;
  }

  @Required()
  public String getSource() {
    return this.source;
  }

  public void setSource(String source) {
    this.source = source;
  }

  @Required()
  public String getDescription() {
    return this.description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  @Required()
  @EnumValues(value={"SHADOW","LIVE","DELETED","PROPOSED","PENDING_AUTO_VERIFICATION","REJECTED","PENDING_MANUAL_REVIEW","LIVE_REFERRAL"})
  public String getStatus() {
    return this.status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  @Required()
  @EnumValues(value={"THREAT","SAFETY"})
  public String getDecision() {
    return this.decision;
  }

  public void setDecision(String decision) {
    this.decision = decision;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.SignatureEntry");

  /**
   * HashCode implementation for SignatureEntry
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getDomain(),
        getSource(),
        getDescription(),
        getStatus(),
        getDecision());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for SignatureEntry
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof SignatureEntry)) {
      return false;
    }

    SignatureEntry that = (SignatureEntry) other;

    return
        Objects.equals(getDomain(), that.getDomain())
        && Objects.equals(getSource(), that.getSource())
        && Objects.equals(getDescription(), that.getDescription())
        && Objects.equals(getStatus(), that.getStatus())
        && Objects.equals(getDecision(), that.getDecision());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("SignatureEntry(");

    ret.append("domain=");
    ret.append(String.valueOf(domain));
    ret.append(", ");

    ret.append("source=");
    ret.append(String.valueOf(source));
    ret.append(", ");

    ret.append("description=");
    ret.append(String.valueOf(description));
    ret.append(", ");

    ret.append("status=");
    ret.append(String.valueOf(status));
    ret.append(", ");

    ret.append("decision=");
    ret.append(String.valueOf(decision));
    ret.append(")");

    return ret.toString();
  }

}
