package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.EnumValues;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="PermissionObject")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class PermissionObject extends java.lang.Object  {

  /**
   * Statically creates a builder instance for PermissionObject.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of PermissionObject.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String permissionID;
    /**
     * Sets the value of the field "permissionID" to be used for the constructed object.
     * @param permissionID
     *   The value of the "permissionID" field.
     * @return
     *   This builder.
     */
    public Builder withPermissionID(String permissionID) {
      this.permissionID = permissionID;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(PermissionObject instance) {
      instance.setPermissionID(this.permissionID);
    }

    /**
     * Builds an instance of PermissionObject.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public PermissionObject build() {
      PermissionObject instance = new PermissionObject();

      populate(instance);

      return instance;
    }
  };

  private String permissionID;

  @Required()
  @EnumValues(value={"SIGNATURE_READ","SIGNATURE_UPDATE","SIGNATURE_OVERRIDE","DETECTION_READ"})
  public String getPermissionID() {
    return this.permissionID;
  }

  public void setPermissionID(String permissionID) {
    this.permissionID = permissionID;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.PermissionObject");

  /**
   * HashCode implementation for PermissionObject
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getPermissionID());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for PermissionObject
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof PermissionObject)) {
      return false;
    }

    PermissionObject that = (PermissionObject) other;

    return
        Objects.equals(getPermissionID(), that.getPermissionID());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("PermissionObject(");

    ret.append("permissionID=");
    ret.append(String.valueOf(permissionID));
    ret.append(")");

    return ret.toString();
  }

}
