package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="SearchSignaturesResponse")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class SearchSignaturesResponse extends java.lang.Object  {

  /**
   * Statically creates a builder instance for SearchSignaturesResponse.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of SearchSignaturesResponse.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected List<SignatureListItem> items;
    /**
     * Sets the value of the field "items" to be used for the constructed object.
     * @param items
     *   The value of the "items" field.
     * @return
     *   This builder.
     */
    public Builder withItems(List<SignatureListItem> items) {
      this.items = items;
      return this;
    }

    protected String nextToken;
    /**
     * Sets the value of the field "nextToken" to be used for the constructed object.
     * @param nextToken
     *   The value of the "nextToken" field.
     * @return
     *   This builder.
     */
    public Builder withNextToken(String nextToken) {
      this.nextToken = nextToken;
      return this;
    }

    protected int totalCount;
    /**
     * Sets the value of the field "totalCount" to be used for the constructed object.
     * @param totalCount
     *   The value of the "totalCount" field.
     * @return
     *   This builder.
     */
    public Builder withTotalCount(int totalCount) {
      this.totalCount = totalCount;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(SearchSignaturesResponse instance) {
      instance.setItems(this.items);
      instance.setNextToken(this.nextToken);
      instance.setTotalCount(this.totalCount);
    }

    /**
     * Builds an instance of SearchSignaturesResponse.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public SearchSignaturesResponse build() {
      SearchSignaturesResponse instance = new SearchSignaturesResponse();

      populate(instance);

      return instance;
    }
  };

  private List<SignatureListItem> items;
  private String nextToken;
  private int totalCount;

  @Required()
  public List<SignatureListItem> getItems() {
    return this.items;
  }

  public void setItems(List<SignatureListItem> items) {
    this.items = items;
  }

  public String getNextToken() {
    return this.nextToken;
  }

  public void setNextToken(String nextToken) {
    this.nextToken = nextToken;
  }

  public int getTotalCount() {
    return this.totalCount;
  }

  public void setTotalCount(int totalCount) {
    this.totalCount = totalCount;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.SearchSignaturesResponse");

  /**
   * HashCode implementation for SearchSignaturesResponse
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getItems(),
        getNextToken(),
        getTotalCount());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for SearchSignaturesResponse
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof SearchSignaturesResponse)) {
      return false;
    }

    SearchSignaturesResponse that = (SearchSignaturesResponse) other;

    return
        Objects.equals(getItems(), that.getItems())
        && Objects.equals(getNextToken(), that.getNextToken())
        && Objects.equals(getTotalCount(), that.getTotalCount());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("SearchSignaturesResponse(");

    ret.append("items=");
    ret.append(String.valueOf(items));
    ret.append(", ");

    ret.append("nextToken=");
    ret.append(String.valueOf(nextToken));
    ret.append(", ");

    ret.append("totalCount=");
    ret.append(String.valueOf(totalCount));
    ret.append(")");

    return ret.toString();
  }

}
