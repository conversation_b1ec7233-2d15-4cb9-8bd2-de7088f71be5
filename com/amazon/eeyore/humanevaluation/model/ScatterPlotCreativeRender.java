package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.EnumValues;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="ScatterPlotCreativeRender")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ScatterPlotCreativeRender extends java.lang.Object  {

  /**
   * Statically creates a builder instance for ScatterPlotCreativeRender.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ScatterPlotCreativeRender.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String creativeID;
    /**
     * Sets the value of the field "creativeID" to be used for the constructed object.
     * @param creativeID
     *   The value of the "creativeID" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeID(String creativeID) {
      this.creativeID = creativeID;
      return this;
    }

    protected String creativeIDSpace;
    /**
     * Sets the value of the field "creativeIDSpace" to be used for the constructed object.
     * @param creativeIDSpace
     *   The value of the "creativeIDSpace" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeIDSpace(String creativeIDSpace) {
      this.creativeIDSpace = creativeIDSpace;
      return this;
    }

    protected String renderID;
    /**
     * Sets the value of the field "renderID" to be used for the constructed object.
     * @param renderID
     *   The value of the "renderID" field.
     * @return
     *   This builder.
     */
    public Builder withRenderID(String renderID) {
      this.renderID = renderID;
      return this;
    }

    protected List<Double> embeddings;
    /**
     * Sets the value of the field "embeddings" to be used for the constructed object.
     * @param embeddings
     *   The value of the "embeddings" field.
     * @return
     *   This builder.
     */
    public Builder withEmbeddings(List<Double> embeddings) {
      this.embeddings = embeddings;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ScatterPlotCreativeRender instance) {
      instance.setCreativeID(this.creativeID);
      instance.setCreativeIDSpace(this.creativeIDSpace);
      instance.setRenderID(this.renderID);
      instance.setEmbeddings(this.embeddings);
    }

    /**
     * Builds an instance of ScatterPlotCreativeRender.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ScatterPlotCreativeRender build() {
      ScatterPlotCreativeRender instance = new ScatterPlotCreativeRender();

      populate(instance);

      return instance;
    }
  };

  private String creativeID;
  private String creativeIDSpace;
  private String renderID;
  private List<Double> embeddings;

  @Required()
  public String getCreativeID() {
    return this.creativeID;
  }

  public void setCreativeID(String creativeID) {
    this.creativeID = creativeID;
  }

  @EnumValues(value={"eriskayCreativeIDSpace","eriskayRodeoCfidSpace","eriskayAxiomIDSpace","eriskayAaxCridSpace","eriskayCanaryIDSpace","eriskayIntegIDSpace","eriskayLoadTestIDSpace"})
  @Required()
  public String getCreativeIDSpace() {
    return this.creativeIDSpace;
  }

  public void setCreativeIDSpace(String creativeIDSpace) {
    this.creativeIDSpace = creativeIDSpace;
  }

  @Required()
  public String getRenderID() {
    return this.renderID;
  }

  public void setRenderID(String renderID) {
    this.renderID = renderID;
  }

  @ListMemberConstraint(@NestedConstraints())
  public List<Double> getEmbeddings() {
    return this.embeddings;
  }

  public void setEmbeddings(List<Double> embeddings) {
    this.embeddings = embeddings;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ScatterPlotCreativeRender");

  /**
   * HashCode implementation for ScatterPlotCreativeRender
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getCreativeID(),
        getCreativeIDSpace(),
        getRenderID(),
        getEmbeddings());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ScatterPlotCreativeRender
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ScatterPlotCreativeRender)) {
      return false;
    }

    ScatterPlotCreativeRender that = (ScatterPlotCreativeRender) other;

    return
        Objects.equals(getCreativeID(), that.getCreativeID())
        && Objects.equals(getCreativeIDSpace(), that.getCreativeIDSpace())
        && Objects.equals(getRenderID(), that.getRenderID())
        && Objects.equals(getEmbeddings(), that.getEmbeddings());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("ScatterPlotCreativeRender(");

    ret.append("creativeID=");
    ret.append(String.valueOf(creativeID));
    ret.append(", ");

    ret.append("creativeIDSpace=");
    ret.append(String.valueOf(creativeIDSpace));
    ret.append(", ");

    ret.append("renderID=");
    ret.append(String.valueOf(renderID));
    ret.append(", ");

    ret.append("embeddings=");
    ret.append(String.valueOf(embeddings));
    ret.append(")");

    return ret.toString();
  }

}
