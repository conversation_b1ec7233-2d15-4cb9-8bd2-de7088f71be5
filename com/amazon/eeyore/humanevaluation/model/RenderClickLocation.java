package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="RenderClickLocation")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class RenderClickLocation extends java.lang.Object  {

  /**
   * Statically creates a builder instance for RenderClickLocation.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of RenderClickLocation.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected int x;
    /**
     * Sets the value of the field "x" to be used for the constructed object.
     * @param x
     *   The value of the "x" field.
     * @return
     *   This builder.
     */
    public Builder withX(int x) {
      this.x = x;
      return this;
    }

    protected int y;
    /**
     * Sets the value of the field "y" to be used for the constructed object.
     * @param y
     *   The value of the "y" field.
     * @return
     *   This builder.
     */
    public Builder withY(int y) {
      this.y = y;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(RenderClickLocation instance) {
      instance.setX(this.x);
      instance.setY(this.y);
    }

    /**
     * Builds an instance of RenderClickLocation.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public RenderClickLocation build() {
      RenderClickLocation instance = new RenderClickLocation();

      populate(instance);

      return instance;
    }
  };

  private int x;
  private int y;

  @Required()
  public int getX() {
    return this.x;
  }

  public void setX(int x) {
    this.x = x;
  }

  @Required()
  public int getY() {
    return this.y;
  }

  public void setY(int y) {
    this.y = y;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.RenderClickLocation");

  /**
   * HashCode implementation for RenderClickLocation
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getX(),
        getY());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for RenderClickLocation
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof RenderClickLocation)) {
      return false;
    }

    RenderClickLocation that = (RenderClickLocation) other;

    return
        Objects.equals(getX(), that.getX())
        && Objects.equals(getY(), that.getY());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("RenderClickLocation(");

    ret.append("x=");
    ret.append(String.valueOf(x));
    ret.append(", ");

    ret.append("y=");
    ret.append(String.valueOf(y));
    ret.append(")");

    return ret.toString();
  }

}
