package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="CreativeDetails")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class CreativeDetails extends java.lang.Object  {

  /**
   * Statically creates a builder instance for CreativeDetails.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of CreativeDetails.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String sourceSystem;
    /**
     * Sets the value of the field "sourceSystem" to be used for the constructed object.
     * @param sourceSystem
     *   The value of the "sourceSystem" field.
     * @return
     *   This builder.
     */
    public Builder withSourceSystem(String sourceSystem) {
      this.sourceSystem = sourceSystem;
      return this;
    }

    protected String sourceProgram;
    /**
     * Sets the value of the field "sourceProgram" to be used for the constructed object.
     * @param sourceProgram
     *   The value of the "sourceProgram" field.
     * @return
     *   This builder.
     */
    public Builder withSourceProgram(String sourceProgram) {
      this.sourceProgram = sourceProgram;
      return this;
    }

    protected List<CreativeId> creativeIds;
    /**
     * Sets the value of the field "creativeIds" to be used for the constructed object.
     * @param creativeIds
     *   The value of the "creativeIds" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeIds(List<CreativeId> creativeIds) {
      this.creativeIds = creativeIds;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(CreativeDetails instance) {
      instance.setSourceSystem(this.sourceSystem);
      instance.setSourceProgram(this.sourceProgram);
      instance.setCreativeIds(this.creativeIds);
    }

    /**
     * Builds an instance of CreativeDetails.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public CreativeDetails build() {
      CreativeDetails instance = new CreativeDetails();

      populate(instance);

      return instance;
    }
  };

  private String sourceSystem;
  private String sourceProgram;
  private List<CreativeId> creativeIds;

  @Required()
  public String getSourceSystem() {
    return this.sourceSystem;
  }

  public void setSourceSystem(String sourceSystem) {
    this.sourceSystem = sourceSystem;
  }

  @Required()
  public String getSourceProgram() {
    return this.sourceProgram;
  }

  public void setSourceProgram(String sourceProgram) {
    this.sourceProgram = sourceProgram;
  }

  @Required()
  public List<CreativeId> getCreativeIds() {
    return this.creativeIds;
  }

  public void setCreativeIds(List<CreativeId> creativeIds) {
    this.creativeIds = creativeIds;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.CreativeDetails");

  /**
   * HashCode implementation for CreativeDetails
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getSourceSystem(),
        getSourceProgram(),
        getCreativeIds());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for CreativeDetails
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof CreativeDetails)) {
      return false;
    }

    CreativeDetails that = (CreativeDetails) other;

    return
        Objects.equals(getSourceSystem(), that.getSourceSystem())
        && Objects.equals(getSourceProgram(), that.getSourceProgram())
        && Objects.equals(getCreativeIds(), that.getCreativeIds());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("CreativeDetails(");

    ret.append("sourceSystem=");
    ret.append(String.valueOf(sourceSystem));
    ret.append(", ");

    ret.append("sourceProgram=");
    ret.append(String.valueOf(sourceProgram));
    ret.append(", ");

    ret.append("creativeIds=");
    ret.append(String.valueOf(creativeIds));
    ret.append(")");

    return ret.toString();
  }

}
