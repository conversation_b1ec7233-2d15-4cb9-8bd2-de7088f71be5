package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.AwsQueryError;
import com.amazon.coral.annotation.AwsSoap11Error;
import com.amazon.coral.annotation.AwsSoap12Error;
import com.amazon.coral.annotation.BsfError;
import com.amazon.coral.annotation.Documentation;
import com.amazon.coral.annotation.Fault;
import com.amazon.coral.annotation.HttpError;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

/**
 * This exception is thrown in case of an internal error.
 */
@Documentation("This exception is thrown in case of an internal error.")
@Shape
@HttpError(httpCode=500)
@AwsSoap11Error(code="InternalException",httpCode=500,type=com.amazon.coral.annotation.AwsErrorType.Receiver)
@AwsQueryError(code="InternalException",httpCode=500,type=com.amazon.coral.annotation.AwsQueryErrorType.Receiver)
@AwsSoap12Error(code="InternalException",httpCode=500,type=com.amazon.coral.annotation.AwsErrorType.Receiver)
@Fault()
@BsfError(value=com.amazon.coral.annotation.BsfErrorType.ServiceFailure)
@XmlName(value="InternalException")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class InternalException extends RuntimeException  {

  /**
   * Statically creates a builder instance for InternalException.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of InternalException.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String message;
    /**
     * Sets the value of the field "message" to be used for the constructed object.
     * @param message
     *   The value of the "message" field.
     * @return
     *   This builder.
     */
    public Builder withMessage(String message) {
      this.message = message;
      return this;
    }

    private java.lang.Throwable cause;
    /**
     * Sets the cause of this exception.
     * @param cause
     *   The exception cause.
     * @return
     *   This builder.
     */
    public Builder withCause(java.lang.Throwable cause) {
      this.cause = cause;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(InternalException instance) {
    }

    /**
     * Builds an instance of InternalException.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public InternalException build() {
      InternalException instance = new InternalException(message, cause);

      populate(instance);

      return instance;
    }
  };

  private static final long serialVersionUID = -1L;

  public InternalException() {
  }

  public InternalException(Throwable cause) {
    initCause(cause);
  }

  public InternalException(String message) {
    super(message);
  }

  public InternalException(String message, Throwable cause) {
    super(message);
    initCause(cause);
  }

  @Override
  public String getMessage() { 
    return super.getMessage();
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.InternalException");

  /**
   * HashCode implementation for InternalException
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getMessage());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for InternalException
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof InternalException)) {
      return false;
    }

    InternalException that = (InternalException) other;

    return
        Objects.equals(getMessage(), that.getMessage());
  }

}
