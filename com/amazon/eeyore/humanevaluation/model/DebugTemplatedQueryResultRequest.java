package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="DebugTemplatedQueryResultRequest")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class DebugTemplatedQueryResultRequest extends java.lang.Object  {

  /**
   * Statically creates a builder instance for DebugTemplatedQueryResultRequest.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of DebugTemplatedQueryResultRequest.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String queryExecutionId;
    /**
     * Sets the value of the field "queryExecutionId" to be used for the constructed object.
     * @param queryExecutionId
     *   The value of the "queryExecutionId" field.
     * @return
     *   This builder.
     */
    public Builder withQueryExecutionId(String queryExecutionId) {
      this.queryExecutionId = queryExecutionId;
      return this;
    }

    protected String continuationToken;
    /**
     * Sets the value of the field "continuationToken" to be used for the constructed object.
     * @param continuationToken
     *   The value of the "continuationToken" field.
     * @return
     *   This builder.
     */
    public Builder withContinuationToken(String continuationToken) {
      this.continuationToken = continuationToken;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(DebugTemplatedQueryResultRequest instance) {
      instance.setQueryExecutionId(this.queryExecutionId);
      instance.setContinuationToken(this.continuationToken);
    }

    /**
     * Builds an instance of DebugTemplatedQueryResultRequest.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public DebugTemplatedQueryResultRequest build() {
      DebugTemplatedQueryResultRequest instance = new DebugTemplatedQueryResultRequest();

      populate(instance);

      return instance;
    }
  };

  private String queryExecutionId;
  private String continuationToken;

  @Required()
  public String getQueryExecutionId() {
    return this.queryExecutionId;
  }

  public void setQueryExecutionId(String queryExecutionId) {
    this.queryExecutionId = queryExecutionId;
  }

  public String getContinuationToken() {
    return this.continuationToken;
  }

  public void setContinuationToken(String continuationToken) {
    this.continuationToken = continuationToken;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.DebugTemplatedQueryResultRequest");

  /**
   * HashCode implementation for DebugTemplatedQueryResultRequest
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getQueryExecutionId(),
        getContinuationToken());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for DebugTemplatedQueryResultRequest
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof DebugTemplatedQueryResultRequest)) {
      return false;
    }

    DebugTemplatedQueryResultRequest that = (DebugTemplatedQueryResultRequest) other;

    return
        Objects.equals(getQueryExecutionId(), that.getQueryExecutionId())
        && Objects.equals(getContinuationToken(), that.getContinuationToken());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("DebugTemplatedQueryResultRequest(");

    ret.append("queryExecutionId=");
    ret.append(String.valueOf(queryExecutionId));
    ret.append(", ");

    ret.append("continuationToken=");
    ret.append(String.valueOf(continuationToken));
    ret.append(")");

    return ret.toString();
  }

}
