package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="DebugTemplatedQueryStartRequest")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class DebugTemplatedQueryStartRequest extends java.lang.Object  {

  /**
   * Statically creates a builder instance for DebugTemplatedQueryStartRequest.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of DebugTemplatedQueryStartRequest.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String templateId;
    /**
     * Sets the value of the field "templateId" to be used for the constructed object.
     * @param templateId
     *   The value of the "templateId" field.
     * @return
     *   This builder.
     */
    public Builder withTemplateId(String templateId) {
      this.templateId = templateId;
      return this;
    }

    protected String params;
    /**
     * Sets the value of the field "params" to be used for the constructed object.
     * @param params
     *   The value of the "params" field.
     * @return
     *   This builder.
     */
    public Builder withParams(String params) {
      this.params = params;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(DebugTemplatedQueryStartRequest instance) {
      instance.setTemplateId(this.templateId);
      instance.setParams(this.params);
    }

    /**
     * Builds an instance of DebugTemplatedQueryStartRequest.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public DebugTemplatedQueryStartRequest build() {
      DebugTemplatedQueryStartRequest instance = new DebugTemplatedQueryStartRequest();

      populate(instance);

      return instance;
    }
  };

  private String templateId;
  private String params;

  public String getTemplateId() {
    return this.templateId;
  }

  public void setTemplateId(String templateId) {
    this.templateId = templateId;
  }

  public String getParams() {
    return this.params;
  }

  public void setParams(String params) {
    this.params = params;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.DebugTemplatedQueryStartRequest");

  /**
   * HashCode implementation for DebugTemplatedQueryStartRequest
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getTemplateId(),
        getParams());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for DebugTemplatedQueryStartRequest
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof DebugTemplatedQueryStartRequest)) {
      return false;
    }

    DebugTemplatedQueryStartRequest that = (DebugTemplatedQueryStartRequest) other;

    return
        Objects.equals(getTemplateId(), that.getTemplateId())
        && Objects.equals(getParams(), that.getParams());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("DebugTemplatedQueryStartRequest(");

    ret.append("templateId=");
    ret.append(String.valueOf(templateId));
    ret.append(", ");

    ret.append("params=");
    ret.append(String.valueOf(params));
    ret.append(")");

    return ret.toString();
  }

}
