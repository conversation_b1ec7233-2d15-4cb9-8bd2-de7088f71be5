package com.amazon.eeyore.humaneval.guice

import com.amazon.adrisk.util.modules.SingletonModule
import com.amazon.eeyore.humaneval.service.ThreatStoreService
import com.amazon.eeyore.humanevaluation.model.DetectionStatus
import com.amazon.eeyore.humanevaluation.model.DetectionType
import com.amazon.eeyore.humanevaluation.model.SignatureDecision
import com.amazon.eeyore.humanevaluation.model.SignatureStatus
import com.amazonaws.services.woozlethreatstore.model.AuthenticatedEntity
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureResult
import com.amazonaws.services.woozlethreatstore.model.DetectionDetails
import com.amazonaws.services.woozlethreatstore.model.DomainSignatureContent
import com.amazonaws.services.woozlethreatstore.model.IntelligenceLineage
import com.amazonaws.services.woozlethreatstore.model.SearchDetectionsRequest
import com.amazonaws.services.woozlethreatstore.model.SearchDetectionsResult
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesRequest
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesResult
import com.amazonaws.services.woozlethreatstore.model.SignatureContent
import com.amazonaws.services.woozlethreatstore.model.SignatureListItem
import com.amazonaws.services.woozlethreatstore.model.ThreatClassification
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureResult
import com.google.inject.Provides
import com.google.inject.Singleton
import io.github.oshai.kotlinlogging.KotlinLogging
import java.util.Date
import java.util.UUID

private val logger = KotlinLogging.logger { }

/**
 * Mock Woozle ThreatStore detection object to maintain consistency with signature models.
 * This represents the structure expected by the ThreatStoreActivity when processing Woozle detection results.
 * Uses a simple object with properties that can be accessed dynamically.
 */
class WoozleDetectionItem {
    var detectionId: String? = null
    var detectionType: String? = null
    var signatureId: String? = null
    var status: String? = null
    var creationTime: String? = null
    var landingPage: String? = null
    var lastUpdatedTime: String? = null
}

class TestThreatStoreService : ThreatStoreService {
    private val signatureService = MockSignatureService()
    private val detectionService = MockDetectionService()

    override fun createSignatures(createRequests: List<CreateSignatureRequest>) =
        createRequests.map { createSignature(it) }

    override fun createSignature(createRequest: CreateSignatureRequest) =
        signatureService.createSignature(createRequest)

    override fun validateSignatures(validateRequests: List<ValidateSignatureRequest>) =
        validateRequests.map { validateSignature(it) }

    override fun validateSignature(validateRequest: ValidateSignatureRequest) =
        signatureService.validateSignature(validateRequest)

    override fun searchSignatures(searchRequest: SearchSignaturesRequest) =
        signatureService.searchSignatures(searchRequest)

    override fun searchDetections(searchRequest: SearchDetectionsRequest) =
        detectionService.searchDetections(searchRequest)
}

class MockSignatureService {
    fun createSignature(createRequest: CreateSignatureRequest): CreateSignatureResult {
        val domain = createRequest.content?.domainSignatureContent?.domain ?: "unknown-domain"
        val signatureId = "mock-signature-${UUID.randomUUID()}"
        logger.info { "MockThreatStoreService: Creating signature for domain '$domain' with ID '$signatureId'" }
        return CreateSignatureResult().apply {
            this.signatureId = signatureId
            this.creationTime = Date()
        }
    }

    fun validateSignature(validateRequest: ValidateSignatureRequest): ValidateSignatureResult {
        val domain = validateRequest.content?.domainSignatureContent?.domain ?: "unknown-domain"
        val (status, reason) = when {
            domain.contains("malicious") || domain.contains("spam") -> "Rejected" to "Domain flagged as potentially malicious"
            domain.contains("test-reject") -> "Rejected" to "Test rejection for validation"
            domain.isBlank() -> "Rejected" to "Domain cannot be empty"
            else -> "Approved" to "Domain validation passed"
        }
        logger.info { "MockThreatStoreService: Validating domain '$domain' -> Status: $status, Reason: $reason" }
        return ValidateSignatureResult().apply {
            this.status = status.uppercase()
            this.reason = reason
        }
    }

    fun searchSignatures(searchRequest: SearchSignaturesRequest): SearchSignaturesResult {
        val status = searchRequest.status
        val maxResults = searchRequest.maxResults ?: 50
        val nextToken = searchRequest.nextToken
        logger.info { "MockThreatStoreService: Searching signatures with status '$status', maxResults: $maxResults" }
        val mockSignatures = MockDataGenerator.generateSignatures(status, maxResults, nextToken)
        val responseNextToken = if (mockSignatures.size >= maxResults && nextToken == null) {
            "mock-next-token-${System.currentTimeMillis()}"
        } else {
            null
        }
        return SearchSignaturesResult().withItems(mockSignatures).withNextToken(responseNextToken)
    }
}

class MockDetectionService {
    fun searchDetections(searchRequest: SearchDetectionsRequest): SearchDetectionsResult {
        val signatureId = searchRequest.signatureId ?: "unknown-signature"
        val detectionType = searchRequest.detectionType ?: "unknown-type"
        val maxResults = searchRequest.maxResults ?: 50
        val nextToken = searchRequest.nextToken
        logger.info { "MockThreatStoreService: Searching detections for signature '$signatureId' of type '$detectionType', maxResults: $maxResults" }
        val mockDetections = MockDataGenerator.generateDetections(signatureId, detectionType, maxResults, nextToken)
        val responseNextToken = if (mockDetections.size >= maxResults && nextToken == null) {
            "mock-detection-token-${System.currentTimeMillis()}"
        } else {
            null
        }
        return SearchDetectionsResult().withItems(mockDetections).withNextToken(responseNextToken)
    }
}

object MockDataGenerator {
    fun generateSignatures(status: String, maxResults: Int, nextToken: String?): List<SignatureListItem> {
        val baseSignatures = listOf(
            createMockSignature("example.com", SignatureStatus.LIVE, SignatureDecision.THREAT, "test-user", "auditor-1", "HUMAN", "Malicious domain"),
            createMockSignature("malicious-site.net", SignatureStatus.SHADOW, SignatureDecision.THREAT, "test-user", "auditor-2", "ADRISK_WEB_THREAT_HUNTING", "Suspicious activity detected"),
            createMockSignature("safe-domain.org", SignatureStatus.LIVE_REFERRAL, SignatureDecision.SAFETY, "test-user", "auditor-1", "GOOGLE", "Safe domain for referral"),
            createMockSignature("test-domain.com", SignatureStatus.PENDING_MANUAL_REVIEW, SignatureDecision.THREAT, "test-user", "auditor-3", "CONFIANT", "Pending review"),
            createMockSignature("blocked-site.evil", SignatureStatus.REJECTED, SignatureDecision.THREAT, "test-user", "auditor-2", "TMT", "Rejected due to false positive"),
        )
        val filtered = baseSignatures.filter { it.status.equals(status, ignoreCase = true) }
        val startIndex = if (nextToken != null) 2 else 0
        val endIndex = minOf(startIndex + maxResults, filtered.size)
        return if (startIndex >= filtered.size) emptyList() else filtered.subList(startIndex, endIndex)
    }

    fun generateDetections(signatureId: String, detectionType: String, maxResults: Int, nextToken: String?): List<WoozleDetectionItem> {
        val baseDetections = listOf(
            createMockDetection("mock-detection-1", detectionType, signatureId, DetectionStatus.PENDING_VERIFICATION, "https://example.com/landing"),
            createMockDetection("mock-detection-2", detectionType, signatureId, DetectionStatus.UNSAFE, "https://malicious.com/page"),
            createMockDetection("mock-detection-3", detectionType, signatureId, DetectionStatus.FALSE_POSITIVE, "https://safe-site.com/page"),
            createMockDetection("mock-detection-4", detectionType, signatureId, DetectionStatus.PENDING_VERIFICATION, "https://test.com/page"),
            createMockDetection("mock-detection-5", detectionType, signatureId, DetectionStatus.UNSAFE, "https://threat.com/page")
        )
        val startIndex = if (nextToken != null) 2 else 0
        val endIndex = minOf(startIndex + maxResults, baseDetections.size)
        return if (startIndex >= baseDetections.size) emptyList() else baseDetections.subList(startIndex, endIndex)
    }

    private fun createMockDetection(detectionId: String, detectionType: String, signatureId: String, status: String, landingPage: String): WoozleDetectionItem {
        return WoozleDetectionItem().apply {
            this.detectionId = "$detectionId-${UUID.randomUUID()}"
            this.detectionType = detectionType
            this.signatureId = signatureId
            this.status = status
            this.creationTime = Date().toString()
            this.landingPage = landingPage
            this.lastUpdatedTime = Date().toString()
        }
    }

    private fun createMockSignature(domain: String, status: String, decisionType: String, creator: String,
                                    auditor: String, source: String, description: String): SignatureListItem {
        return SignatureListItem()
            .withSignatureId("mock-sig-${UUID.randomUUID()}")
            .withSignatureDecisionType(decisionType)
            .withStatus(status)
            .withCreator(AuthenticatedEntity().withAuthenticatedUser(creator))
            .withAuditor(auditor)
            .withSource(source)
            .withDescription(description)
            .withCreationTime(Date())
            .withContent(SignatureContent().withDomainSignatureContent(DomainSignatureContent().withDomain(domain)))
    }
}

class TestWoozleServiceModule : SingletonModule() {
    @Provides
    @Singleton
    fun threatStoreService(): ThreatStoreService = TestThreatStoreService()
}